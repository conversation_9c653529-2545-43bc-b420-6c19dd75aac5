{"name": "Microsoft TRELLIS", "description": "Microsoft TRELLIS - Advanced 3D generation from single images using structured latent representations", "features": ["Single image to 3D", "Multi-image to 3D", "High-quality mesh generation", "Gaussian splatting output", "GLB export", "Texture generation"], "dependencies": {"system": [{"name": "Trellis Server", "description": "Trellis API server with integrated virtual environment and models", "check_command": "run.bat", "install_path": "pipelines/3DPipelines/gen3d/trellis-stable-projectorz-101/run.bat", "required": true, "system_package": true}]}, "api": {"host": "127.0.0.1", "port": 7960, "endpoints": {"ping": "/ping", "generate": "/generate", "generate_multi": "/generate_multi", "status": "/status"}}, "settings": {"default": {"seed": 1, "ss_steps": 12, "ss_cfg_strength": 7.5, "slat_steps": 12, "slat_cfg_strength": 3.0, "simplify": 0.95, "texture_size": 1024}, "presets": {"fast": {"ss_steps": 8, "slat_steps": 8, "texture_size": 512}, "quality": {"ss_steps": 16, "slat_steps": 16, "texture_size": 2048}}}}