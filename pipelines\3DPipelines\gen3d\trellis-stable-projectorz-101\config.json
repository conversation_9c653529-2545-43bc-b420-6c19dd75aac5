{"name": "Microsoft TRELLIS", "description": "Microsoft TRELLIS - Advanced 3D generation from single images using structured latent representations", "features": ["Single image to 3D", "Multi-image to 3D", "High-quality mesh generation", "Gaussian splatting output", "GLB export", "Texture generation"], "dependencies": {"system": [{"name": "Trellis Server", "description": "Trellis API server running on port 7960", "check_command": "run-fp16.bat", "install_path": "pipelines/3DPipelines/gen3d/trellis-stable-projectorz-101/run-fp16.bat", "required": true}], "python": ["torch>=2.1.0", "torchvision>=0.16.0", "transformers>=4.36.0", "diffusers>=0.25.0", "accelerate>=0.26.0", "xformers>=0.0.23", "pillow>=10.0.0", "numpy>=1.24.0", "imageio>=2.31.0", "trimesh>=4.0.0", "rembg>=2.0.50", "gradio>=4.44.1", "gradio_litmodel3d>=0.0.1"], "models": [{"name": "TRELLIS-image-large", "repo_id": "jetx/TRELLIS-image-large", "required": true, "description": "Large image-to-3D model (1.2B parameters)", "local_path": "trellis-stable-projectorz-101/models/TRELLIS-image-large"}]}, "api": {"host": "127.0.0.1", "port": 7960, "endpoints": {"ping": "/ping", "generate": "/generate", "generate_multi": "/generate_multi", "status": "/status"}}, "settings": {"default": {"seed": 1, "ss_steps": 12, "ss_cfg_strength": 7.5, "slat_steps": 12, "slat_cfg_strength": 3.0, "simplify": 0.95, "texture_size": 1024}, "presets": {"fast": {"ss_steps": 8, "slat_steps": 8, "texture_size": 512}, "quality": {"ss_steps": 16, "slat_steps": 16, "texture_size": 2048}}}}