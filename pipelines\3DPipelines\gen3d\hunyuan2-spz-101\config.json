{"name": "Hunyuan3D-2", "description": "Tencent Hunyuan3D-2.0 - Advanced 3D generation with multi-view support and high-quality texture generation", "features": ["Single image to 3D", "Multi-view to 3D", "Text to 3D", "High-quality mesh generation", "Advanced texture generation", "GLB export", "Multiple quality presets", "Octree-based generation"], "dependencies": {"system": [{"name": "Hunyuan3D-2 Server", "description": "Hunyuan3D-2 API server running on port 7960", "check_command": "run-stableprojectorz-full-multiview.bat", "install_path": "pipelines/3DPipelines/gen3d/hunyuan2-spz-101/run-projectorz_(faster)/run-stableprojectorz-full-multiview.bat", "required": true}], "python": ["torch>=2.0.0", "torchvision>=0.15.0", "transformers>=4.36.0", "diffusers>=0.25.0", "accelerate>=0.26.0", "xformers>=0.0.23", "pillow>=10.0.0", "numpy>=1.24.0", "trimesh>=4.0.0", "rembg>=2.0.50", "gradio>=4.44.1", "fastapi>=0.104.0", "uvicorn>=0.24.0", "pydantic>=2.5.0"], "models": [{"name": "Hunyuan3D-2mini", "repo_id": "tencent/Hunyuan3D-2mini", "required": true, "description": "Hunyuan3D-2 Mini model for fast generation", "local_path": "hunyuan2-spz-101/models/Hunyuan3D-2mini"}, {"name": "Hunyuan3D-2", "repo_id": "tencent/Hunyuan3D-2", "required": false, "description": "Full Hunyuan3D-2 model for high-quality generation", "local_path": "hunyuan2-spz-101/models/Hunyuan3D-2"}, {"name": "Hunyuan3D-2mv", "repo_id": "tencent/Hunyuan3D-2mv", "required": false, "description": "Hunyuan3D-2 Multi-view model", "local_path": "hunyuan2-spz-101/models/Hunyuan3D-2mv"}]}, "api": {"host": "127.0.0.1", "port": 7960, "endpoints": {"ping": "/ping", "generate": "/generate", "generate_multi": "/generate_multi", "status": "/status"}}, "settings": {"default": {"seed": 1234, "guidance_scale": 5.5, "num_inference_steps": 25, "octree_resolution": 256, "texture_size": 1024, "mesh_simplify_ratio": 0.15, "num_chunks": 30}, "presets": {"low": {"name": "Low (Fast)", "guidance_scale": 3.0, "num_inference_steps": 15, "octree_resolution": 128, "texture_size": 512}, "medium": {"name": "Medium (Average Wait)", "guidance_scale": 5.5, "num_inference_steps": 25, "octree_resolution": 256, "texture_size": 1024}, "high": {"name": "High (Extended Wait)", "guidance_scale": 7.5, "num_inference_steps": 35, "octree_resolution": 384, "texture_size": 1536}, "ultra": {"name": "Ultra (<PERSON><PERSON><PERSON>)", "guidance_scale": 10.0, "num_inference_steps": 50, "octree_resolution": 512, "texture_size": 2048}}}}