2025-07-14 14:01:22 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 14:01:22 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-14 14:15:46 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-14 14:22:21 | ERROR | hunyuan3d_api | Failed to load texture pipeline: [WinError 1314] A required privilege is not held by the client: '..\\..\\..\\..\\blobs\\8d1412e50d46389d40b29f824034ff4ba4f973ca' -> 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--tencent--Hunyuan3D-2\\snapshots\\34e28261f71c32975727be8db0eace439a280f82\\hunyuan3d-delight-v2-0\\text_encoder\\config.json'
2025-07-14 14:22:21 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-14 14:22:21 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-14 14:22:21 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-14 14:34:09 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 14:34:09 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-14 14:34:23 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-14 14:40:51 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 14:40:51 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-14 14:40:52 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 14:40:52 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-14 14:40:55 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 14:40:55 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-14 14:41:05 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-14 14:41:05 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-14 14:57:51 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 14:57:51 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-14 14:58:04 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-14 14:59:18 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-14 14:59:18 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-14 14:59:18 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-14 14:59:18 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-14 14:59:20 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-14 14:59:20 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-14 14:59:20 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-14 14:59:23 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-14 15:00:33 | INFO | hunyuan3d_api | Processing mesh (faces: 354440)
2025-07-14 15:00:34 | INFO | hunyuan3d_api | Reducing faces to 336718
2025-07-14 15:00:37 | INFO | hunyuan3d_api | Skipping texture generation for this request
2025-07-14 15:00:37 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 4.24 seconds
2025-07-14 15:00:37 | INFO | hunyuan3d_api | Generation completed in 76.82 seconds
2025-07-14 15:00:37 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-14 15:13:17 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 15:13:17 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-14 15:13:30 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-14 15:14:44 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-14 15:14:44 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-14 15:14:44 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-14 15:14:44 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-14 15:14:45 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-14 15:14:46 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-14 15:14:46 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-14 15:14:49 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-14 15:15:49 | INFO | hunyuan3d_api | Processing mesh (faces: 352268)
2025-07-14 15:15:51 | INFO | hunyuan3d_api | Reducing faces to 334654
2025-07-14 15:15:53 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-14 15:15:56 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-14 15:15:56 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-14 15:15:56 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-14 15:15:56 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-14 15:15:56 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-14 15:19:46 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-14 15:19:46 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-14 15:19:47 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-14 15:20:31 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-14 15:20:31 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-14 15:20:40 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-14 15:20:43 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 293.99 seconds
2025-07-14 15:20:43 | INFO | hunyuan3d_api | Generation completed in 357.82 seconds
2025-07-14 15:46:08 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 15:46:09 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-14 15:46:21 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-14 15:47:34 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-14 15:47:34 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-14 15:47:34 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-14 15:47:34 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-14 15:47:36 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-14 15:47:36 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-14 15:47:36 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-14 15:47:40 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-14 15:48:39 | INFO | hunyuan3d_api | Processing mesh (faces: 354312)
2025-07-14 15:48:41 | INFO | hunyuan3d_api | Reducing faces to 336596
2025-07-14 15:48:43 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-14 15:48:46 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-14 15:48:46 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-14 15:48:46 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-14 15:48:46 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-14 15:48:46 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-14 15:52:03 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-14 15:52:04 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-14 15:52:05 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-14 15:52:52 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-14 15:52:52 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-14 15:53:01 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-14 15:53:04 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 264.63 seconds
2025-07-14 15:53:04 | INFO | hunyuan3d_api | Generation completed in 327.80 seconds
2025-07-14 15:53:04 | INFO | hunyuan3d_api | Client is downloading a model.
