========================================
         3D AI Studio Launcher
========================================

[1/3] Checking Node.js installation...
Γ£ô Node.js found
[2/3] Checking for node_modules...
Γ£ô Dependencies ready
[3/3] Building frontend for production...
The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
vite v5.4.19 building for production...

(!) outDir N:\3D AI Studio\dist\renderer is not inside project root and will not be emptied.
Use --emptyOutDir to override.

✓ 2113 modules transformed.
../../dist/renderer/index.html                     0.21 kB │ gzip:   0.16 kB
../../dist/renderer/assets/index-StoDjayT.css     44.55 kB │ gzip:   7.34 kB
../../dist/renderer/assets/index-E2ArCN4p.js      11.52 kB │ gzip:   4.64 kB
../../dist/renderer/assets/index-DEiAwzqk.js   1,344.27 kB │ gzip: 367.19 kB

(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
✓ built in 13.89s
Γ£ô Frontend build complete

========================================
    Starting 3D AI Studio (Production)...
========================================


> 3d-ai-studio@1.0.0 start
> electron .


info: Logger initialized. {"service":"user-service","timestamp":"2025-07-14 14:32:38"}
info: App is ready. {"service":"user-service","timestamp":"2025-07-14 14:32:38"}
info: Console window hidden at startup {"service":"user-service","timestamp":"2025-07-14 14:32:38"}
[Trellis Server] Checking for lingering Python processes...
[Trellis Server] Found Python processes, killing them...
[Trellis Server] Processes found: python.exe                   45316 Console                    1 12,693,652 K
[Trellis Server] Successfully killed Python processes
info: Python process cleanup completed on startup {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Production mode - Loading file: N:\3D AI Studio\dist\renderer\index.html {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Main window web contents started loading {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Splash screen initialization complete {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Splash Status: Loading pipeline configurations... {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Splash Status: Scanning pipelines... {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Loading pipelines from embedded configurations... {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Loaded pipeline: Core {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Loaded pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Loaded pipeline: ImageUpscaling {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Loaded pipeline: trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Loaded pipeline: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Ensured pipeline directory exists: Core {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Ensured pipeline directory exists: ImageGeneration {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Ensured pipeline directory exists: ImageUpscaling {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Skipping directory creation for system package pipeline: trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Skipping directory creation for system package pipeline: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Registering pipelines... {"service":"user-service","timestamp":"2025-07-14 14:32:42"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-14 14:32:43"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-14 14:32:43"}
info: Main window web contents finished loading {"service":"user-service","timestamp":"2025-07-14 14:32:43"}
info: Main window finished loading {"service":"user-service","timestamp":"2025-07-14 14:32:43"}
info: Splash screen displayed {"service":"user-service","timestamp":"2025-07-14 14:32:43"}
info: IPC: get-console-visibility called {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loading 21 sample images {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 1/21: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 2/21: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 3/21: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 4/21: Green Flower Pot {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 5/21: Blue Couch {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 6/21: Little Girl {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 7/21: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 8/21: Lantern {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 9/21: Wooden Barrel {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 10/21: Cut Log Piece {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 11/21: White Chair {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 12/21: Greek Statue {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 13/21: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 14/21: Stylized Tree {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 15/21: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 16/21: White Backpack {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 17/21: Potted Plant {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 18/21: Red Myan Pot {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 19/21: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 20/21: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: Loaded sample 21/21: Brown Pot {"service":"user-service","timestamp":"2025-07-14 14:32:44"}
info: IPC: get-config called for key: huggingface-token {"service":"user-service","timestamp":"2025-07-14 14:32:45"}
info: [DEBUG] Found pipeline config.json files: {"0":"N:\\3D AI Studio\\pipelines\\3DPipelines\\gen3d\\hunyuan2-spz-101\\config.json","1":"N:\\3D AI Studio\\pipelines\\3DPipelines\\gen3d\\trellis-stable-projectorz-101\\config.json","2":"N:\\3D AI Studio\\pipelines\\Core\\config.json","3":"N:\\3D AI Studio\\pipelines\\ImageGeneration\\config.json","4":"N:\\3D AI Studio\\pipelines\\ImageUpscaling\\config.json","service":"user-service","timestamp":"2025-07-14 14:32:57"}
info: Registered pipeline: hunyuan2-spz-101 (from N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\config.json) {"service":"user-service","timestamp":"2025-07-14 14:32:57"}
info: Registered pipeline: trellis-stable-projectorz-101 (from N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\config.json) {"service":"user-service","timestamp":"2025-07-14 14:32:57"}
info: Registered pipeline: Core (from N:\3D AI Studio\pipelines\Core\config.json) {"service":"user-service","timestamp":"2025-07-14 14:32:57"}
info: Registered pipeline: ImageGeneration (from N:\3D AI Studio\pipelines\ImageGeneration\config.json) {"service":"user-service","timestamp":"2025-07-14 14:32:57"}
info: Registered pipeline: ImageUpscaling (from N:\3D AI Studio\pipelines\ImageUpscaling\config.json) {"service":"user-service","timestamp":"2025-07-14 14:32:57"}
info: [DEBUG] Final registered pipelines: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-14 14:32:57"}
info: PipelineLoader: Final pipelines after merge: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-14 14:32:57"}
info: Splash Status: Checking core dependencies... {"service":"user-service","timestamp":"2025-07-14 14:32:57"}
info: Checking dependencies for Core {"service":"user-service","timestamp":"2025-07-14 14:32:57"}
info: Final dependency check for Core: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-14 14:33:00"}
info: Splash Status: Core dependencies verified {"service":"user-service","timestamp":"2025-07-14 14:33:00"}
info: Splash Status: Finalizing startup... {"service":"user-service","timestamp":"2025-07-14 14:33:00"}
info: Startup complete - showing main window {"service":"user-service","timestamp":"2025-07-14 14:33:03"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-14 14:33:03"}
info: Initialization complete signal sent to renderer {"service":"user-service","timestamp":"2025-07-14 14:33:03"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-14 14:33:03"}
info: IPC: get-available-pipelines called {"service":"user-service","timestamp":"2025-07-14 14:33:03"}
info: [DEBUG] getRegisteredPipelines returning: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-14 14:33:03"}
info: [upload-file] Received ΓÇô filename: mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure.jpg, size: 34600 bytes (Uint8Array) {"service":"user-service","timestamp":"2025-07-14 14:33:21"}
info: Buffer received (bytes): 34600 {"service":"user-service","timestamp":"2025-07-14 14:33:21"}
info: Uploaded original file saved: N:\3D AI Studio\uploads\1168b184-c0e7-4af4-b7f8-08d141455476\mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure.jpg {"service":"user-service","timestamp":"2025-07-14 14:33:21"}
info: Starting background removal for N:\3D AI Studio\uploads\1168b184-c0e7-4af4-b7f8-08d141455476\mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure.jpg... {"service":"user-service","timestamp":"2025-07-14 14:33:21"}
info: Spawning: N:\3D AI Studio\pipelines\Core\env\Scripts\python.exe N:\3D AI Studio\src\main\python_helpers\remove_background.py N:\3D AI Studio\uploads\1168b184-c0e7-4af4-b7f8-08d141455476\mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure.jpg N:\3D AI Studio\uploads\1168b184-c0e7-4af4-b7f8-08d141455476\mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure_processed.png {"service":"user-service","timestamp":"2025-07-14 14:33:21"}
info: [load-file] Received request for: uploads/1168b184-c0e7-4af4-b7f8-08d141455476/mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure_processed.png {"service":"user-service","timestamp":"2025-07-14 14:33:28"}
info: [load-file] Reading absolute path: N:\3D AI Studio\uploads\1168b184-c0e7-4af4-b7f8-08d141455476\mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure_processed.png {"service":"user-service","timestamp":"2025-07-14 14:33:28"}
info: IPC: run-pipeline called for: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-14 14:33:43"}
info: runPipeline request: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-14 14:33:43"}
info: runPipeline: Registered pipelines at call time: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-14 14:33:43"}
info: Checking dependencies for hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-14 14:33:43"}
info: Final dependency check for hunyuan2-spz-101: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-14 14:33:43"}
[IPC Handler] Sending status: hunyaun - 25% - Launching Hunyaun server and generating 3D model...
[HunyaunServer] generate3DModel called with imagePath: uploads/1168b184-c0e7-4af4-b7f8-08d141455476/mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure_processed.png
[HunyaunServer] Settings received: {
  ss_steps: 12,
  ss_cfg_strength: 7.5,
  slat_steps: 12,
  slat_cfg_strength: 3,
  randomize_seed: true,
  seed: 619300,
  simplify: 0.95,
  texture_size: 1024,
  enable_lighting_optimizer: true,
  octree_resolution: 128,
  num_inference_steps: 5,
  guidance_scale: 5,
  enable_texture: false,
  face_count: 40000
}
[HunyaunServer] Checking for lingering Python processes...
[HunyaunServer] No Python processes found to clean up (or cleanup failed)
[HunyaunServer] isHunyaunRunning() returned: false
[HunyaunServer] [Hunyaun Progress] Stage tracking reset for new generation
[HunyaunServer] Server not running, starting server...
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] Starting server...
[HunyaunServer] RUN_BAT: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\run-projectorz_(faster)\run-stableprojectorz-full-multiview.bat
[HunyaunServer] Batch file exists: true
[HunyaunServer] Hunyaun server process started
[HunyaunServer] startHunyaunServer() called successfully
[HunyaunServer] Waiting for Hunyaun server to be ready...
[HunyaunServer] [STDOUT] N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe"
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\get-pip.py"
[HunyaunServer] [STDOUT] DEBUG: VENV_PATH is "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv"
[HunyaunServer] [STDOUT] DEBUG: Checking for "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\activate.bat"
[HunyaunServer] [STDOUT] DEBUG: Virtual environment already exists, skipping creation
[HunyaunServer] [STDOUT]         1 file(s) copied.
[HunyaunServer] [STDOUT] Portable Python located at: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe
[HunyaunServer] [STDOUT] Virtual environment Python set to: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[HunyaunServer] [STDOUT] Starting the server, please wait...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [Hunyaun Progress] hunyaun_preprocessing: 0% - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-14 14:34:07"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] [System Info] Python: 3.11.9   | PyTorch: 2.5.1+cu124 | CUDA: 12.4
[HunyaunServer] [STDOUT] Initializing Hunyuan3D models from tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv
[HunyaunServer] [STDERR] 2025-07-14 14:34:09,033 - hy3dgen.shapgen - INFO - Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv

[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] 2025-07-14 14:34:09 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
[HunyaunServer] [STDERR] 2025-07-14 14:34:09,034 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface

[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Fetching 3 files: 100%|##########| 3/3 [00:00<?, ?it/s]

[HunyaunServer] [STDOUT] 2025-07-14 14:34:23 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
[HunyaunServer] [STDERR] 2025-07-14 14:34:23,933 - hy3dgen.shapgen - INFO - Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors

[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-14 14:34:42"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
[HunyaunServer] [STDERR] N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Lib\site-packages\huggingface_hub\file_download.py:142: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.
To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development
  warnings.warn(message)

Fetching 13 files:  31%|###       | 4/13 [00:00<00:00, 10.82it/s]
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-14 14:35:22"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Fetching 13 files:  31%|###       | 4/13 [00:14<00:00, 10.82it/s]
Fetching 13 files: 100%|##########| 13/13 [00:18<00:00,  1.42s/it]

Fetching 17 files:   0%|          | 0/17 [00:00<?, ?it/s]
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
Fetching 17 files:   6%|5         | 1/17 [00:00<00:04,  3.30it/s]
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-14 14:36:02"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-14 14:36:53"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-14 14:37:43"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-14 14:38:33"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-14 14:39:23"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-14 14:40:14"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] ≡ƒöä Auto-recovery triggered after 200 failed attempts (attempt 1/3)
[HunyaunServer] ≡ƒöº Starting auto-recovery process (attempt 1/3)...
[HunyaunServer] ≡ƒº╣ Cleaning up Python processes...
[HunyaunServer] Checking for lingering Python processes...
[HunyaunServer] Python processes cleaned up successfully
[HunyaunServer] ≡ƒ¢æ Terminating existing Hunyaun process...
[HunyaunServer] ΓÅ│ Waiting for cleanup to complete...
[HunyaunServer] Hunyaun server process exited with code: null
[HunyaunServer] ≡ƒöä Auto-recovery triggered after 200 failed attempts (attempt 2/3)
[HunyaunServer] ≡ƒöº Starting auto-recovery process (attempt 2/3)...
[HunyaunServer] ≡ƒº╣ Cleaning up Python processes...
[HunyaunServer] Checking for lingering Python processes...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] No Python processes found to clean up (or cleanup failed)
[HunyaunServer] ΓÅ│ Waiting for cleanup to complete...
[HunyaunServer] ≡ƒöä Auto-recovery triggered after 200 failed attempts (attempt 3/3)
[HunyaunServer] ≡ƒöº Starting auto-recovery process (attempt 3/3)...
[HunyaunServer] ≡ƒº╣ Cleaning up Python processes...
[HunyaunServer] Checking for lingering Python processes...
[HunyaunServer] No Python processes found to clean up (or cleanup failed)
[HunyaunServer] ΓÅ│ Waiting for cleanup to complete...
[HunyaunServer] ≡ƒÜÇ Restarting Hunyaun server...
[HunyaunServer] Starting server...
[HunyaunServer] RUN_BAT: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\run-projectorz_(faster)\run-stableprojectorz-full-multiview.bat
[HunyaunServer] Batch file exists: true
[HunyaunServer] Hunyaun server process started
[HunyaunServer] Γ£à Auto-recovery process completed (attempt 3/3)
[HunyaunServer] [STDOUT] N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe"
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\get-pip.py"
[HunyaunServer] [STDOUT] DEBUG: VENV_PATH is "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv"
[HunyaunServer] [STDOUT] DEBUG: Checking for "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\activate.bat"
[HunyaunServer] ≡ƒöä Auto-recovery detected, resetting wait counter...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] [STDOUT]         1 file(s) copied.
[HunyaunServer] [STDOUT] Portable Python located at: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe
[HunyaunServer] [STDOUT] Virtual environment Python set to: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[HunyaunServer] [STDOUT] Starting the server, please wait...
[HunyaunServer] ≡ƒÜÇ Restarting Hunyaun server...
[HunyaunServer] Starting server...
[HunyaunServer] RUN_BAT: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\run-projectorz_(faster)\run-stableprojectorz-full-multiview.bat
[HunyaunServer] Batch file exists: true
[HunyaunServer] Killing existing Hunyaun process...
[HunyaunServer] Hunyaun server process started
[HunyaunServer] Γ£à Auto-recovery process completed (attempt 3/3)
[HunyaunServer] [STDOUT] N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe"
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\get-pip.py"
[HunyaunServer] [STDOUT] DEBUG: VENV_PATH is "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv"
[HunyaunServer] [STDOUT] DEBUG: Checking for "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\activate.bat"
[HunyaunServer] [STDOUT] DEBUG: Virtual environment already exists, skipping creation
[HunyaunServer] [STDOUT]         1 file(s) copied.
[HunyaunServer] [STDOUT] Portable Python located at: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe
[HunyaunServer] [STDOUT] Virtual environment Python set to: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[HunyaunServer] [STDOUT] Starting the server, please wait...
[HunyaunServer] ≡ƒÜÇ Restarting Hunyaun server...
[HunyaunServer] Starting server...
[HunyaunServer] RUN_BAT: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\run-projectorz_(faster)\run-stableprojectorz-full-multiview.bat
[HunyaunServer] Batch file exists: true
[HunyaunServer] Killing existing Hunyaun process...
[HunyaunServer] Hunyaun server process started
[HunyaunServer] Γ£à Auto-recovery process completed (attempt 3/3)
[HunyaunServer] [STDOUT] N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe"
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\get-pip.py"
[HunyaunServer] [STDOUT] DEBUG: VENV_PATH is "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv"
[HunyaunServer] [STDOUT] DEBUG: Checking for "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\activate.bat"
[HunyaunServer] [STDOUT] DEBUG: Virtual environment already exists, skipping creation
[HunyaunServer] [STDOUT]         1 file(s) copied.
[HunyaunServer] [STDOUT] Portable Python located at: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe
[HunyaunServer] [STDOUT] Virtual environment Python set to: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Starting the server, please wait...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] [STDOUT] [System Info] Python: 3.11.9   | PyTorch: 2.5.1+cu124 | CUDA: 12.4
[HunyaunServer] [STDOUT] [System Info] Python: 3.11.9   | PyTorch: 2.5.1+cu124 | CUDA: 12.4
[HunyaunServer] [STDERR] 2025-07-14 14:40:51,550 - hy3dgen.shapgen - INFO - Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv

[HunyaunServer] [STDOUT] Initializing Hunyuan3D models from tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv
[HunyaunServer] [STDERR] 2025-07-14 14:40:51,551 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface

[HunyaunServer] [STDOUT] 2025-07-14 14:40:51 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
[HunyaunServer] [STDOUT] Initializing Hunyuan3D models from tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv
[HunyaunServer] [STDERR] 2025-07-14 14:40:52,904 - hy3dgen.shapgen - INFO - Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 14:40:52,905 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface

[HunyaunServer] [STDOUT] 2025-07-14 14:40:52 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
[HunyaunServer] [STDOUT] [System Info] Python: 3.11.9   | PyTorch: 2.5.1+cu124 | CUDA: 12.4
[HunyaunServer] [STDERR] 2025-07-14 14:40:55,375 - hy3dgen.shapgen - INFO - Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv

[HunyaunServer] [STDOUT] Initializing Hunyuan3D models from tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv
[HunyaunServer] [STDERR] 2025-07-14 14:40:55,376 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface

info: [Hunyaun Progress] hunyaun: 0% - Server restarted, waiting for connection... {"service":"user-service","timestamp":"2025-07-14 14:40:58"}
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
Fetching 3 files: 100%|##########| 3/3 [00:00<?, ?it/s]

Fetching 3 files: 100%|##########| 3/3 [00:00<?, ?it/s]

Fetching 3 files: 100%|##########| 3/3 [00:00<?, ?it/s]

[HunyaunServer] [STDERR] 2025-07-14 14:41:05,296 - hy3dgen.shapgen - INFO - Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors

[HunyaunServer] [STDOUT] 2025-07-14 14:41:05 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
[HunyaunServer] [STDOUT] 2025-07-14 14:41:05 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
[HunyaunServer] [STDERR] 2025-07-14 14:41:05,296 - hy3dgen.shapgen - INFO - Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors

[HunyaunServer] [STDERR] 2025-07-14 14:41:05,305 - hy3dgen.shapgen - INFO - Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors

[HunyaunServer] [STDOUT] 2025-07-14 14:41:05 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
info: [Hunyaun Progress] hunyaun: 0% - Server restarted, waiting for connection... {"service":"user-service","timestamp":"2025-07-14 14:41:48"}
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|##########| 13/13 [00:00<00:00, 13050.73it/s]

Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|##########| 13/13 [00:00<00:00, 6505.90it/s]

Fetching 17 files:   0%|          | 0/17 [00:00<?, ?it/s]
Fetching 17 files:   0%|          | 0/17 [00:00<?, ?it/s]
Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|##########| 13/13 [00:00<00:00, 3258.39it/s]

Fetching 17 files:   0%|          | 0/17 [00:00<?, ?it/s]
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
info: [Hunyaun Progress] hunyaun: 0% - Server restarted, waiting for connection... {"service":"user-service","timestamp":"2025-07-14 14:42:38"}
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
info: [Hunyaun Progress] hunyaun: 0% - Server restarted, waiting for connection... {"service":"user-service","timestamp":"2025-07-14 14:43:29"}
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
info: [Hunyaun Progress] hunyaun: 0% - Server restarted, waiting for connection... {"service":"user-service","timestamp":"2025-07-14 14:44:19"}
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
info: [Hunyaun Progress] hunyaun: 0% - Server restarted, waiting for connection... {"service":"user-service","timestamp":"2025-07-14 14:45:09"}
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
info: [Hunyaun Progress] hunyaun: 0% - Server restarted, waiting for connection... {"service":"user-service","timestamp":"2025-07-14 14:45:59"}
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
info: [Hunyaun Progress] hunyaun: 0% - Server restarted, waiting for connection... {"service":"user-service","timestamp":"2025-07-14 14:46:50"}
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
info: [Hunyaun Progress] hunyaun: 0% - Server restarted, waiting for connection... {"service":"user-service","timestamp":"2025-07-14 14:47:40"}
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] [STDERR] N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Lib\site-packages\huggingface_hub\file_download.py:142: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.
To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development
  warnings.warn(message)

[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
Fetching 17 files:  35%|###5      | 6/17 [06:30<11:56, 65.16s/it]
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
info: [Hunyaun Progress] hunyaun: 0% - Server restarted, waiting for connection... {"service":"user-service","timestamp":"2025-07-14 14:48:30"}
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
info: [Hunyaun Progress] hunyaun: 0% - Server restarted, waiting for connection... {"service":"user-service","timestamp":"2025-07-14 14:49:21"}
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[IPC Handler] Sending status: hunyaun - 0% - Server restarted, waiting for connection...
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[HunyaunServer] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.