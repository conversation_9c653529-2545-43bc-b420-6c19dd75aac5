========================================
         3D AI Studio Launcher
========================================

[1/3] Checking Node.js installation...
Γ£ô Node.js found
[2/3] Checking for node_modules...
Γ£ô Dependencies ready
[3/3] Building frontend for production...
The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
vite v5.4.19 building for production...

(!) outDir N:\3D AI Studio\dist\renderer is not inside project root and will not be emptied.
Use --emptyOutDir to override.

✓ 2113 modules transformed.
../../dist/renderer/index.html                     0.21 kB │ gzip:   0.16 kB
../../dist/renderer/assets/index-StoDjayT.css     44.55 kB │ gzip:   7.34 kB
../../dist/renderer/assets/index-E2ArCN4p.js      11.52 kB │ gzip:   4.64 kB
../../dist/renderer/assets/index-DEiAwzqk.js   1,344.27 kB │ gzip: 367.19 kB

(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
✓ built in 13.21s
Γ£ô Frontend build complete

========================================
    Starting 3D AI Studio (Production)...
========================================


> 3d-ai-studio@1.0.0 start
> electron .


info: Logger initialized. {"service":"user-service","timestamp":"2025-07-13 23:59:31"}
info: App is ready. {"service":"user-service","timestamp":"2025-07-13 23:59:31"}
info: Console window hidden at startup {"service":"user-service","timestamp":"2025-07-13 23:59:31"}
[Trellis Server] Checking for lingering Python processes...
[Trellis Server] No Python processes found
info: Python process cleanup completed on startup {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Production mode - Loading file: N:\3D AI Studio\dist\renderer\index.html {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Main window web contents started loading {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Splash screen initialization complete {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Splash Status: Loading pipeline configurations... {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Splash Status: Scanning pipelines... {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Loading pipelines from embedded configurations... {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Loaded pipeline: Core {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Loaded pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Loaded pipeline: ImageUpscaling {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Loaded pipeline: trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Loaded pipeline: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Ensured pipeline directory exists: Core {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Ensured pipeline directory exists: ImageGeneration {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Ensured pipeline directory exists: ImageUpscaling {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Skipping directory creation for system package pipeline: trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Skipping directory creation for system package pipeline: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Registering pipelines... {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Splash screen displayed {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Main window web contents finished loading {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: Main window finished loading {"service":"user-service","timestamp":"2025-07-13 23:59:33"}
info: IPC: get-console-visibility called {"service":"user-service","timestamp":"2025-07-13 23:59:34"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-13 23:59:34"}
info: Loading 21 sample images {"service":"user-service","timestamp":"2025-07-13 23:59:34"}
info: Loaded sample 1/21: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-13 23:59:34"}
info: Loaded sample 2/21: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-13 23:59:34"}
info: Loaded sample 3/21: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-13 23:59:34"}
info: Loaded sample 4/21: Green Flower Pot {"service":"user-service","timestamp":"2025-07-13 23:59:34"}
info: Loaded sample 5/21: Blue Couch {"service":"user-service","timestamp":"2025-07-13 23:59:34"}
info: Loaded sample 6/21: Little Girl {"service":"user-service","timestamp":"2025-07-13 23:59:34"}
info: Loaded sample 7/21: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-13 23:59:34"}
info: Loaded sample 8/21: Lantern {"service":"user-service","timestamp":"2025-07-13 23:59:34"}
info: Loaded sample 9/21: Wooden Barrel {"service":"user-service","timestamp":"2025-07-13 23:59:35"}
info: Loaded sample 10/21: Cut Log Piece {"service":"user-service","timestamp":"2025-07-13 23:59:35"}
info: Loaded sample 11/21: White Chair {"service":"user-service","timestamp":"2025-07-13 23:59:35"}
info: Loaded sample 12/21: Greek Statue {"service":"user-service","timestamp":"2025-07-13 23:59:35"}
info: Loaded sample 13/21: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-13 23:59:35"}
info: Loaded sample 14/21: Stylized Tree {"service":"user-service","timestamp":"2025-07-13 23:59:35"}
info: Loaded sample 15/21: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-13 23:59:35"}
info: Loaded sample 16/21: White Backpack {"service":"user-service","timestamp":"2025-07-13 23:59:35"}
info: Loaded sample 17/21: Potted Plant {"service":"user-service","timestamp":"2025-07-13 23:59:35"}
info: Loaded sample 18/21: Red Myan Pot {"service":"user-service","timestamp":"2025-07-13 23:59:35"}
info: Loaded sample 19/21: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-13 23:59:35"}
info: Loaded sample 20/21: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-13 23:59:35"}
info: Loaded sample 21/21: Brown Pot {"service":"user-service","timestamp":"2025-07-13 23:59:35"}
info: IPC: get-config called for key: huggingface-token {"service":"user-service","timestamp":"2025-07-13 23:59:35"}
info: [DEBUG] Found pipeline config.json files: {"0":"N:\\3D AI Studio\\pipelines\\3DPipelines\\gen3d\\hunyuan2-spz-101\\config.json","1":"N:\\3D AI Studio\\pipelines\\3DPipelines\\gen3d\\trellis-stable-projectorz-101\\config.json","2":"N:\\3D AI Studio\\pipelines\\Core\\config.json","3":"N:\\3D AI Studio\\pipelines\\ImageGeneration\\config.json","4":"N:\\3D AI Studio\\pipelines\\ImageUpscaling\\config.json","service":"user-service","timestamp":"2025-07-13 23:59:47"}
info: Registered pipeline: hunyuan2-spz-101 (from N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\config.json) {"service":"user-service","timestamp":"2025-07-13 23:59:47"}
info: Registered pipeline: trellis-stable-projectorz-101 (from N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\config.json) {"service":"user-service","timestamp":"2025-07-13 23:59:47"}
info: Registered pipeline: Core (from N:\3D AI Studio\pipelines\Core\config.json) {"service":"user-service","timestamp":"2025-07-13 23:59:47"}
info: Registered pipeline: ImageGeneration (from N:\3D AI Studio\pipelines\ImageGeneration\config.json) {"service":"user-service","timestamp":"2025-07-13 23:59:47"}
info: Registered pipeline: ImageUpscaling (from N:\3D AI Studio\pipelines\ImageUpscaling\config.json) {"service":"user-service","timestamp":"2025-07-13 23:59:47"}
info: [DEBUG] Final registered pipelines: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-13 23:59:47"}
info: PipelineLoader: Final pipelines after merge: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-13 23:59:47"}
info: Splash Status: Checking core dependencies... {"service":"user-service","timestamp":"2025-07-13 23:59:47"}
info: Checking dependencies for Core {"service":"user-service","timestamp":"2025-07-13 23:59:47"}
info: Final dependency check for Core: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-13 23:59:50"}
info: Splash Status: Core dependencies verified {"service":"user-service","timestamp":"2025-07-13 23:59:50"}
info: Splash Status: Finalizing startup... {"service":"user-service","timestamp":"2025-07-13 23:59:50"}
info: Startup complete - showing main window {"service":"user-service","timestamp":"2025-07-13 23:59:53"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-13 23:59:53"}
info: Initialization complete signal sent to renderer {"service":"user-service","timestamp":"2025-07-13 23:59:53"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-13 23:59:53"}
info: IPC: get-available-pipelines called {"service":"user-service","timestamp":"2025-07-13 23:59:53"}
info: [DEBUG] getRegisteredPipelines returning: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-13 23:59:53"}
info: [upload-file] Received ΓÇô filename: mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure.jpg, size: 34600 bytes (Uint8Array) {"service":"user-service","timestamp":"2025-07-14 00:00:10"}
info: Buffer received (bytes): 34600 {"service":"user-service","timestamp":"2025-07-14 00:00:10"}
info: Uploaded original file saved: N:\3D AI Studio\uploads\32efb86b-9f5b-4d64-b700-13d2147c2c3b\mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure.jpg {"service":"user-service","timestamp":"2025-07-14 00:00:10"}
info: Starting background removal for N:\3D AI Studio\uploads\32efb86b-9f5b-4d64-b700-13d2147c2c3b\mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure.jpg... {"service":"user-service","timestamp":"2025-07-14 00:00:10"}
info: Spawning: N:\3D AI Studio\pipelines\Core\env\Scripts\python.exe N:\3D AI Studio\src\main\python_helpers\remove_background.py N:\3D AI Studio\uploads\32efb86b-9f5b-4d64-b700-13d2147c2c3b\mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure.jpg N:\3D AI Studio\uploads\32efb86b-9f5b-4d64-b700-13d2147c2c3b\mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure_processed.png {"service":"user-service","timestamp":"2025-07-14 00:00:10"}
info: [load-file] Received request for: uploads/32efb86b-9f5b-4d64-b700-13d2147c2c3b/mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure_processed.png {"service":"user-service","timestamp":"2025-07-14 00:00:17"}
info: [load-file] Reading absolute path: N:\3D AI Studio\uploads\32efb86b-9f5b-4d64-b700-13d2147c2c3b\mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure_processed.png {"service":"user-service","timestamp":"2025-07-14 00:00:17"}
info: IPC: run-pipeline called for: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info: runPipeline request: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info: runPipeline: Registered pipelines at call time: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-14 00:00:19"}
info: Checking dependencies for hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info: Checking model Hunyuan3D-2mini at path: N:\3D AI Studio\models\hunyuan2-spz-101\models\Hunyuan3D-2mini {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info: Model Hunyuan3D-2mini directory does not exist: N:\3D AI Studio\models\hunyuan2-spz-101\models\Hunyuan3D-2mini {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
warn: Required model Hunyuan3D-2mini is not properly installed {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info: Checking model Hunyuan3D-2 at path: N:\3D AI Studio\models\hunyuan2-spz-101\models\Hunyuan3D-2 {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info: Model Hunyuan3D-2 directory does not exist: N:\3D AI Studio\models\hunyuan2-spz-101\models\Hunyuan3D-2 {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info: Checking model Hunyuan3D-2mv at path: N:\3D AI Studio\models\hunyuan2-spz-101\models\Hunyuan3D-2mv {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info: Model Hunyuan3D-2mv directory does not exist: N:\3D AI Studio\models\hunyuan2-spz-101\models\Hunyuan3D-2mv {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
info: Model dependency check for hunyuan2-spz-101: not satisfied {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
warn: Model dependencies not satisfied for hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-14 00:00:19"}
[IPC Handler] Sending status: hunyaun - 25% - Launching Hunyaun server and generating 3D model...
[HunyaunServer] generate3DModel called with imagePath: uploads/32efb86b-9f5b-4d64-b700-13d2147c2c3b/mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure_processed.png
[HunyaunServer] Settings received: {
  ss_steps: 20,
  ss_cfg_strength: 10,
  slat_steps: 20,
  slat_cfg_strength: 4,
  randomize_seed: true,
  seed: 67111,
  simplify: 0.9,
  texture_size: 2048,
  enable_lighting_optimizer: true,
  octree_resolution: 128,
  num_inference_steps: 5,
  guidance_scale: 5,
  enable_texture: false,
  face_count: 40000
}
[HunyaunServer] Checking for lingering Python processes...
[HunyaunServer] No Python processes found to clean up (or cleanup failed)
[HunyaunServer] isHunyaunRunning() returned: false
[HunyaunServer] [Hunyaun Progress] Stage tracking reset for new generation
[HunyaunServer] Server not running, starting server...
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] Starting server...
[HunyaunServer] RUN_BAT: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\run-projectorz_(faster)\run-stableprojectorz-full-multiview.bat
[HunyaunServer] Batch file exists: true
[HunyaunServer] Hunyaun server process started
[HunyaunServer] startHunyaunServer() called successfully
[HunyaunServer] Waiting for Hunyaun server to be ready...
[HunyaunServer] [STDOUT] N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe"
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\get-pip.py"
[HunyaunServer] [STDOUT] Creating virtual environment using portable Python...
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] sitecustomize.py applied
[HunyaunServer] [STDOUT] Failed to create virtual environment using portable Python.
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-14 00:00:37"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
