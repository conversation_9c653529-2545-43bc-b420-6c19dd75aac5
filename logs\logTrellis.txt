========================================
         3D AI Studio Launcher
========================================

[1/3] Checking Node.js installation...
Γ£ô Node.js found
[2/3] Checking for node_modules...
Γ£ô Dependencies ready
[3/3] Building frontend for production...
The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
vite v5.4.19 building for production...

(!) outDir N:\3D AI Studio\dist\renderer is not inside project root and will not be emptied.
Use --emptyOutDir to override.

✓ 2113 modules transformed.
../../dist/renderer/index.html                     0.21 kB │ gzip:   0.16 kB
../../dist/renderer/assets/index-StoDjayT.css     44.55 kB │ gzip:   7.34 kB
../../dist/renderer/assets/index-E2ArCN4p.js      11.52 kB │ gzip:   4.64 kB
../../dist/renderer/assets/index-DEiAwzqk.js   1,344.27 kB │ gzip: 367.19 kB

(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
✓ built in 13.74s
Γ£ô Frontend build complete

========================================
    Starting 3D AI Studio (Production)...
========================================


> 3d-ai-studio@1.0.0 start
> electron .


info: Logger initialized. {"service":"user-service","timestamp":"2025-07-13 23:53:49"}
info: App is ready. {"service":"user-service","timestamp":"2025-07-13 23:53:49"}
info: Console window hidden at startup {"service":"user-service","timestamp":"2025-07-13 23:53:49"}
[Trellis Server] Checking for lingering Python processes...
[Trellis Server] No Python processes found
info: Python process cleanup completed on startup {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Production mode - Loading file: N:\3D AI Studio\dist\renderer\index.html {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Main window web contents started loading {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Splash screen initialization complete {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Splash Status: Loading pipeline configurations... {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Splash Status: Scanning pipelines... {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Loading pipelines from embedded configurations... {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Loaded pipeline: Core {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Loaded pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Loaded pipeline: ImageUpscaling {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Loaded pipeline: trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Loaded pipeline: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Ensured pipeline directory exists: Core {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Ensured pipeline directory exists: ImageGeneration {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Ensured pipeline directory exists: ImageUpscaling {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Skipping directory creation for system package pipeline: trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Skipping directory creation for system package pipeline: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Registering pipelines... {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Main window web contents finished loading {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Main window finished loading {"service":"user-service","timestamp":"2025-07-13 23:53:51"}
info: Splash screen displayed {"service":"user-service","timestamp":"2025-07-13 23:53:52"}
info: IPC: get-console-visibility called {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loading 21 sample images {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 1/21: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 2/21: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 3/21: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 4/21: Green Flower Pot {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 5/21: Blue Couch {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 6/21: Little Girl {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 7/21: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 8/21: Lantern {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 9/21: Wooden Barrel {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 10/21: Cut Log Piece {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 11/21: White Chair {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 12/21: Greek Statue {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 13/21: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 14/21: Stylized Tree {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 15/21: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 16/21: White Backpack {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 17/21: Potted Plant {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 18/21: Red Myan Pot {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 19/21: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 20/21: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: Loaded sample 21/21: Brown Pot {"service":"user-service","timestamp":"2025-07-13 23:53:53"}
info: IPC: get-config called for key: huggingface-token {"service":"user-service","timestamp":"2025-07-13 23:53:54"}
info: [DEBUG] Found pipeline config.json files: {"0":"N:\\3D AI Studio\\pipelines\\3DPipelines\\gen3d\\hunyuan2-spz-101\\config.json","1":"N:\\3D AI Studio\\pipelines\\3DPipelines\\gen3d\\trellis-stable-projectorz-101\\config.json","2":"N:\\3D AI Studio\\pipelines\\Core\\config.json","3":"N:\\3D AI Studio\\pipelines\\ImageGeneration\\config.json","4":"N:\\3D AI Studio\\pipelines\\ImageUpscaling\\config.json","service":"user-service","timestamp":"2025-07-13 23:54:06"}
info: Registered pipeline: hunyuan2-spz-101 (from N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\config.json) {"service":"user-service","timestamp":"2025-07-13 23:54:06"}
info: Registered pipeline: trellis-stable-projectorz-101 (from N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\config.json) {"service":"user-service","timestamp":"2025-07-13 23:54:06"}
info: Registered pipeline: Core (from N:\3D AI Studio\pipelines\Core\config.json) {"service":"user-service","timestamp":"2025-07-13 23:54:06"}
info: Registered pipeline: ImageGeneration (from N:\3D AI Studio\pipelines\ImageGeneration\config.json) {"service":"user-service","timestamp":"2025-07-13 23:54:06"}
info: Registered pipeline: ImageUpscaling (from N:\3D AI Studio\pipelines\ImageUpscaling\config.json) {"service":"user-service","timestamp":"2025-07-13 23:54:06"}
info: [DEBUG] Final registered pipelines: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-13 23:54:06"}
info: PipelineLoader: Final pipelines after merge: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-13 23:54:06"}
info: Splash Status: Checking core dependencies... {"service":"user-service","timestamp":"2025-07-13 23:54:06"}
info: Checking dependencies for Core {"service":"user-service","timestamp":"2025-07-13 23:54:06"}
info: Final dependency check for Core: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-13 23:54:09"}
info: Splash Status: Core dependencies verified {"service":"user-service","timestamp":"2025-07-13 23:54:09"}
info: Splash Status: Finalizing startup... {"service":"user-service","timestamp":"2025-07-13 23:54:09"}
info: Startup complete - showing main window {"service":"user-service","timestamp":"2025-07-13 23:54:12"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-13 23:54:12"}
info: Initialization complete signal sent to renderer {"service":"user-service","timestamp":"2025-07-13 23:54:12"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-13 23:54:12"}
info: IPC: get-available-pipelines called {"service":"user-service","timestamp":"2025-07-13 23:54:12"}
info: [DEBUG] getRegisteredPipelines returning: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-13 23:54:12"}
info: [upload-file] Received ΓÇô filename: mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure.jpg, size: 34600 bytes (Uint8Array) {"service":"user-service","timestamp":"2025-07-13 23:54:37"}
info: Buffer received (bytes): 34600 {"service":"user-service","timestamp":"2025-07-13 23:54:37"}
info: Uploaded original file saved: N:\3D AI Studio\uploads\a078f9c8-fc00-4d84-bb12-01013a1e5bea\mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure.jpg {"service":"user-service","timestamp":"2025-07-13 23:54:37"}
info: Starting background removal for N:\3D AI Studio\uploads\a078f9c8-fc00-4d84-bb12-01013a1e5bea\mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure.jpg... {"service":"user-service","timestamp":"2025-07-13 23:54:37"}
info: Spawning: N:\3D AI Studio\pipelines\Core\env\Scripts\python.exe N:\3D AI Studio\src\main\python_helpers\remove_background.py N:\3D AI Studio\uploads\a078f9c8-fc00-4d84-bb12-01013a1e5bea\mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure.jpg N:\3D AI Studio\uploads\a078f9c8-fc00-4d84-bb12-01013a1e5bea\mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure_processed.png {"service":"user-service","timestamp":"2025-07-13 23:54:37"}
info: [load-file] Received request for: uploads/a078f9c8-fc00-4d84-bb12-01013a1e5bea/mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure_processed.png {"service":"user-service","timestamp":"2025-07-13 23:54:44"}
info: [load-file] Reading absolute path: N:\3D AI Studio\uploads\a078f9c8-fc00-4d84-bb12-01013a1e5bea\mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure_processed.png {"service":"user-service","timestamp":"2025-07-13 23:54:44"}
info: IPC: run-pipeline called for: trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-07-13 23:54:45"}
info: runPipeline request: trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-07-13 23:54:45"}
info: runPipeline: Registered pipelines at call time: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-13 23:54:45"}
info: Checking dependencies for trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-07-13 23:54:45"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-13 23:54:45"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-13 23:54:45"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-13 23:54:45"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-13 23:54:45"}
info: Checking model TRELLIS-image-large at path: N:\3D AI Studio\models\trellis-stable-projectorz-101\models\TRELLIS-image-large {"service":"user-service","timestamp":"2025-07-13 23:54:45"}
info: Model TRELLIS-image-large directory does not exist: N:\3D AI Studio\models\trellis-stable-projectorz-101\models\TRELLIS-image-large {"service":"user-service","timestamp":"2025-07-13 23:54:45"}
warn: Required model TRELLIS-image-large is not properly installed {"service":"user-service","timestamp":"2025-07-13 23:54:45"}
info: Model dependency check for trellis-stable-projectorz-101: not satisfied {"service":"user-service","timestamp":"2025-07-13 23:54:45"}
warn: Model dependencies not satisfied for trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-07-13 23:54:45"}
[Trellis Server] generate3DModel called with imagePath: uploads/a078f9c8-fc00-4d84-bb12-01013a1e5bea/mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_Mayan_Pot_In_The_Form_Of_A_Kneeling_Figure_processed.png
[Trellis Server] Checking for lingering Python processes...
[Trellis Server] No Python processes found
[Trellis Server] [Trellis Progress] Stage tracking reset for new generation
[Trellis Server] Starting Trellis server...
[IPC Handler] Sending status: preprocessing - 0% - Loading image and initializing pipeline
[Trellis Server] Starting server...
[Trellis Server] RUN_BAT: N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\run-fp16.bat
[Trellis Server] Batch file exists: true
[Trellis Server] Working directory: N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101
[Trellis Server] Command: cmd.exe /c run-fp16.bat
[Pipeline Loader] Progress update: trellis - undefined% - Waiting for Trellis server to start...
[IPC Handler] Sending status: trellis - 0% - Waiting for Trellis server to start...
[Pipeline Loader] Progress update: trellis - undefined% - Waiting for Trellis server to start...
[IPC Handler] Sending status: trellis - 0% - Waiting for Trellis server to start...
[Pipeline Loader] Progress update: trellis - undefined% - Waiting for Trellis server to start...
[IPC Handler] Sending status: trellis - 0% - Waiting for Trellis server to start...
[Trellis Server] ≡ƒöä Auto-recovery triggered after 80 failed attempts (attempt 1/3)
[Trellis Server] ≡ƒöº Starting auto-recovery process (attempt 1/3)...
[Trellis Server] ≡ƒº╣ Cleaning up Python processes...
[Trellis Server] Checking for lingering Python processes...
[Trellis Server] Found Python processes, killing them...
[Trellis Server] Processes found: python.exe                   28980 Console                    1    621,592 K
[Trellis Server] ≡ƒöä Auto-recovery triggered after 80 failed attempts (attempt 2/3)
[Trellis Server] ≡ƒöº Starting auto-recovery process (attempt 2/3)...
[Trellis Server] ≡ƒº╣ Cleaning up Python processes...
[Trellis Server] Checking for lingering Python processes...
[Trellis Server] Successfully killed Python processes
[Trellis Server] Process exited with code 1
[Trellis Server] No Python processes found
[Trellis Server] ≡ƒ¢æ Terminating existing Trellis process...
[Trellis Server] ΓÅ│ Waiting for cleanup to complete...
[Trellis Server] ≡ƒöä Auto-recovery triggered after 80 failed attempts (attempt 3/3)
[Trellis Server] ≡ƒöº Starting auto-recovery process (attempt 3/3)...
[Trellis Server] ≡ƒº╣ Cleaning up Python processes...
[Trellis Server] Checking for lingering Python processes...
[Trellis Server] ΓÅ│ Waiting for cleanup to complete...
[Trellis Server] No Python processes found
[Trellis Server] ΓÅ│ Waiting for cleanup to complete...
[Trellis Server] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[Trellis Server] Γ¥î Max recovery attempts (3) reached. Server may be permanently stuck.
[Trellis Server] ≡ƒÜÇ Restarting Trellis server...
[Trellis Server] Starting server...
[Trellis Server] RUN_BAT: N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\run-fp16.bat
[Trellis Server] Batch file exists: true
[Trellis Server] Working directory: N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101
[Trellis Server] Command: cmd.exe /c run-fp16.bat
[Trellis Server] Γ£à Auto-recovery process completed (attempt 3/3)
[Trellis Server] ≡ƒÜÇ Restarting Trellis server...
[Trellis Server] Starting server...
[Trellis Server] RUN_BAT: N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\run-fp16.bat
[Trellis Server] Batch file exists: true
[Trellis Server] Working directory: N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101
[Trellis Server] Command: cmd.exe /c run-fp16.bat
[Trellis Server] Γ£à Auto-recovery process completed (attempt 3/3)
[Trellis Server] ≡ƒöä Auto-recovery detected, resetting wait counter...
[Trellis Server] ≡ƒÜÇ Restarting Trellis server...
[Trellis Server] Starting server...
[Trellis Server] RUN_BAT: N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\run-fp16.bat
[Trellis Server] Batch file exists: true
[Trellis Server] Working directory: N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101
[Trellis Server] Command: cmd.exe /c run-fp16.bat
[Trellis Server] Γ£à Auto-recovery process completed (attempt 3/3)
[Trellis Server] [Trellis Server] Failed to create virtual environment using portable Python.
[Trellis Server] Process exited with code 0
[Pipeline Loader] Progress update: trellis - undefined% - Server restarted, waiting for connection...
[IPC Handler] Sending status: trellis - 0% - Server restarted, waiting for connection...
