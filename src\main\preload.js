// All of the Node.js APIs are available in the preload process.
// It has the same sandbox as a Chrome extension.
const { contextBridge, ipcRenderer } = require('electron');

// We will expose backend functions to the renderer process here
// using contextBridge.
contextBridge.exposeInMainWorld('electronAPI', {
  // General
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // Config
  getConfig: (key) => ipcRenderer.invoke('get-config', key),
  setConfig: (key, value) => ipcRenderer.invoke('set-config', key, value),
  validateHFToken: (token) => ipcRenderer.invoke('validate-hf-token', token),
  
  // Pipelines
  getAvailablePipelines: () => ipcRenderer.invoke('get-available-pipelines'),
  runPipeline: (name, data) => ipcRenderer.invoke('run-pipeline', name, data),
  onPipelineStatus: (callback) => {
    const listener = (_event, value) => callback(value);
    ipcRenderer.on('pipeline-status', listener);
    return () => ipcRenderer.removeListener('pipeline-status', listener);
  },
  
  // Projects
  getProjects: () => ipcRenderer.invoke('get-projects'),
  getProjectDetails: (projectId) => ipcRenderer.invoke('get-project-details', projectId),
  updateProject: (id, data) => ipcRenderer.invoke('update-project', id, data),
  deleteProject: (projectId) => ipcRenderer.invoke('delete-project', projectId),
  createProjectFromImage: (data) => ipcRenderer.invoke('create-project-from-image', data),
  createProjectFrom3DModel: (data) => ipcRenderer.invoke('create-project-from-3d-model', data),
  downloadFile: (fileUrl) => ipcRenderer.invoke('download-file', fileUrl),
  loadFile: (relativePath) => ipcRenderer.invoke('load-file', relativePath),
  replaceProjectImage: (projectId, upscaledDataUrl, imageFormat) => ipcRenderer.invoke('replaceProjectImage', projectId, upscaledDataUrl, imageFormat),
  
  // Sample Images
  getSampleImages: () => ipcRenderer.invoke('get-sample-images'),
  uploadSampleImage: (data) => ipcRenderer.invoke('upload-sample-image', data),
  updateSampleImage: (filename, data) => ipcRenderer.invoke('update-sample-image', filename, data),
  deleteSampleImage: (filename) => ipcRenderer.invoke('delete-sample-image', filename),

  // Logs
  getLogs: () => ipcRenderer.invoke('get-logs'),
  clearLogs: () => ipcRenderer.invoke('clear-logs'),

  // Console Window
  toggleConsole: () => ipcRenderer.invoke('toggle-console'),
  getConsoleVisibility: () => ipcRenderer.invoke('get-console-visibility'),
  onLogUpdate: (callback) => {
    const listener = (_event, value) => callback(value);
    ipcRenderer.on('log-update', listener);
    return () => ipcRenderer.removeListener('log-update', listener);
  },
  
  // Dependency Manager
  getDependencyStatus: () => ipcRenderer.invoke('get-dependency-status'),
  installDependencies: (pipelineName, component, name) => ipcRenderer.invoke('install-dependencies', pipelineName, component, name),
  onInstallationProgress: (callback) => {
    const listener = (_event, value) => callback(value);
    ipcRenderer.on('installation-progress', listener);
    return () => ipcRenderer.removeListener('installation-progress', listener);
  },

  // Misc
  uploadFile: (file) => ipcRenderer.invoke('upload-file', file),
  fetchModel: (url) => ipcRenderer.invoke('fetch-model', url),
  checkModelExists: (url) => ipcRenderer.invoke('check-model-exists', url),

  // Listen for log messages from the main process
  onLog: (callback) => ipcRenderer.on('log-message', (_event, message) => callback(message)),

  onStatusProgress: (callback) => {
    const listener = (_event, value) => callback(value);
    ipcRenderer.on('status-progress', listener);
    return () => ipcRenderer.removeListener('status-progress', listener);
  },

  // Image Generation
  generateImage: (data) => ipcRenderer.invoke('generate-image', data),
  generateImageStream: (data) => ipcRenderer.invoke('generate-image-stream', data),
  onImageGenerationProgress: (callback) => {
    const listener = (_event, value) => callback(value);
    ipcRenderer.on('image-generation-progress', listener);
    return () => ipcRenderer.removeListener('image-generation-progress', listener);
  },

  // Image Gallery
  getImageCollections: () => ipcRenderer.invoke('get-image-collections'),
  saveImageCollections: (collections) => ipcRenderer.invoke('save-image-collections', collections),
  addImageToGallery: (data) => ipcRenderer.invoke('add-image-to-gallery', data),
  deleteImageFromGallery: (data) => ipcRenderer.invoke('delete-image-from-gallery', data),

  // Upscaling
  getTempFilePath: (filename) => ipcRenderer.invoke('get-temp-file-path', filename),
  saveDataUrlToTempFile: (dataUrl) => ipcRenderer.invoke('save-data-url-to-temp-file', dataUrl),
  invoke: (channel, data) => ipcRenderer.invoke(channel, data),
  on: (channel, callback) => {
    const listener = (_event, value) => callback(value);
    ipcRenderer.on(channel, listener);
    return () => ipcRenderer.removeListener(channel, listener);
  },
  removeListener: (channel, callback) => ipcRenderer.removeListener(channel, callback),

  // Add this function for saving uploaded 3D model files
  saveUploadedModelFile: async (file) => {
    // Convert file to buffer
    const buffer = await file.arrayBuffer();
    const result = await ipcRenderer.invoke('upload-file', { buffer: new Uint8Array(buffer), filename: file.name });
    // Return the saved file path (result.path)
    return result && result.path ? result.path : null;
  },
});